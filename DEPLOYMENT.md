# Todo List 部署指南

本文档详细说明如何在生产环境中部署 Todo List 应用。

## 生产环境要求

### 服务器环境
- **操作系统**: Ubuntu 20.04+ / CentOS 8+ / Debian 11+
- **Web服务器**: Nginx 1.18+ 或 Apache 2.4+
- **PHP**: 8.0+ (推荐 8.1+)
- **数据库**: MySQL 8.0+ 或 MariaDB 10.5+
- **Node.js**: 16.0+ (用于构建前端)

### PHP扩展要求
```bash
# 必需扩展
php-fpm
php-mysql
php-json
php-mbstring
php-xml
php-curl
php-zip
php-gd
```

## 部署步骤

### 1. 服务器准备

#### 安装基础环境 (Ubuntu)
```bash
# 更新系统
sudo apt update && sudo apt upgrade -y

# 安装Nginx
sudo apt install nginx -y

# 安装PHP 8.1
sudo apt install software-properties-common -y
sudo add-apt-repository ppa:ondrej/php -y
sudo apt update
sudo apt install php8.1-fpm php8.1-mysql php8.1-json php8.1-mbstring php8.1-xml php8.1-curl php8.1-zip php8.1-gd -y

# 安装MySQL
sudo apt install mysql-server -y

# 安装Node.js
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt install nodejs -y

# 安装Composer
curl -sS https://getcomposer.org/installer | php
sudo mv composer.phar /usr/local/bin/composer
```

### 2. 数据库配置

```bash
# 登录MySQL
sudo mysql -u root -p

# 创建数据库和用户
CREATE DATABASE todo_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER 'todo_user'@'localhost' IDENTIFIED BY 'your_strong_password';
GRANT ALL PRIVILEGES ON todo_db.* TO 'todo_user'@'localhost';
FLUSH PRIVILEGES;
EXIT;

# 导入数据表结构
mysql -u todo_user -p todo_db < /path/to/backend/database/init.sql
```

### 3. 后端部署

```bash
# 创建项目目录
sudo mkdir -p /var/www/todo-app
sudo chown -R $USER:$USER /var/www/todo-app

# 上传代码到服务器
cd /var/www/todo-app
# 使用git clone或scp上传代码

# 安装后端依赖
cd backend
composer install --no-dev --optimize-autoloader

# 配置环境变量
cp .env.example .env
vim .env
```

#### 生产环境配置 (.env)
```ini
APP_DEBUG = false

[APP]
DEFAULT_TIMEZONE = Asia/Shanghai

[DATABASE]
TYPE = mysql
HOSTNAME = localhost
DATABASE = todo_db
USERNAME = todo_user
PASSWORD = your_strong_password
HOSTPORT = 3306
CHARSET = utf8mb4
DEBUG = false

[JWT]
SECRET_KEY = your-very-secure-secret-key-change-this
EXPIRE_TIME = 7200
```

```bash
# 设置目录权限
sudo chown -R www-data:www-data /var/www/todo-app/backend/runtime
sudo chmod -R 755 /var/www/todo-app/backend/runtime
```

### 4. 前端构建

```bash
# 构建前端
cd /var/www/todo-app/frontend
npm install
npm run build

# 构建完成后，dist目录包含生产版本文件
```

### 5. Nginx配置

创建Nginx配置文件：
```bash
sudo vim /etc/nginx/sites-available/todo-app
```

```nginx
server {
    listen 80;
    server_name your-domain.com;
    root /var/www/todo-app/frontend/dist;
    index index.html;

    # 前端路由
    location / {
        try_files $uri $uri/ /index.html;
    }

    # API代理
    location /api {
        try_files $uri $uri/ /index.php?$query_string;
        root /var/www/todo-app/backend/public;
        
        location ~ \.php$ {
            fastcgi_pass unix:/var/run/php/php8.1-fpm.sock;
            fastcgi_index index.php;
            fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
            include fastcgi_params;
        }
    }

    # 静态资源缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # 安全配置
    location ~ /\.ht {
        deny all;
    }
    
    location ~ /\.env {
        deny all;
    }
}
```

```bash
# 启用站点
sudo ln -s /etc/nginx/sites-available/todo-app /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

### 6. SSL证书配置 (推荐)

使用Let's Encrypt免费SSL证书：
```bash
# 安装Certbot
sudo apt install certbot python3-certbot-nginx -y

# 获取SSL证书
sudo certbot --nginx -d your-domain.com

# 自动续期
sudo crontab -e
# 添加以下行
0 12 * * * /usr/bin/certbot renew --quiet
```

### 7. 进程管理 (可选)

使用Supervisor管理PHP-FPM：
```bash
sudo apt install supervisor -y

# 创建配置文件
sudo vim /etc/supervisor/conf.d/todo-app.conf
```

```ini
[program:todo-app-worker]
process_name=%(program_name)s_%(process_num)02d
command=php /var/www/todo-app/backend/think queue:work
directory=/var/www/todo-app/backend
autostart=true
autorestart=true
user=www-data
numprocs=1
redirect_stderr=true
stdout_logfile=/var/log/supervisor/todo-app-worker.log
```

```bash
sudo supervisorctl reread
sudo supervisorctl update
sudo supervisorctl start todo-app-worker:*
```

## 性能优化

### 1. PHP优化
```ini
# /etc/php/8.1/fpm/php.ini
memory_limit = 256M
max_execution_time = 60
upload_max_filesize = 10M
post_max_size = 10M

# OPcache配置
opcache.enable=1
opcache.memory_consumption=128
opcache.max_accelerated_files=4000
opcache.revalidate_freq=2
```

### 2. MySQL优化
```ini
# /etc/mysql/mysql.conf.d/mysqld.cnf
[mysqld]
innodb_buffer_pool_size = 256M
innodb_log_file_size = 64M
query_cache_type = 1
query_cache_size = 32M
```

### 3. Nginx优化
```nginx
# 启用gzip压缩
gzip on;
gzip_vary on;
gzip_min_length 1024;
gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;

# 连接优化
keepalive_timeout 65;
client_max_body_size 10M;
```

## 监控和日志

### 1. 日志配置
```bash
# 创建日志目录
sudo mkdir -p /var/log/todo-app
sudo chown www-data:www-data /var/log/todo-app

# 配置日志轮转
sudo vim /etc/logrotate.d/todo-app
```

```
/var/log/todo-app/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 www-data www-data
}
```

### 2. 健康检查脚本
```bash
#!/bin/bash
# /usr/local/bin/todo-health-check.sh

API_URL="https://your-domain.com/api/"
RESPONSE=$(curl -s -o /dev/null -w "%{http_code}" $API_URL)

if [ $RESPONSE -eq 200 ]; then
    echo "$(date): API健康检查通过"
else
    echo "$(date): API健康检查失败，状态码: $RESPONSE"
    # 可以添加告警逻辑
fi
```

## 备份策略

### 1. 数据库备份
```bash
#!/bin/bash
# /usr/local/bin/backup-db.sh

BACKUP_DIR="/var/backups/todo-app"
DATE=$(date +%Y%m%d_%H%M%S)

mkdir -p $BACKUP_DIR

mysqldump -u todo_user -p'your_password' todo_db > $BACKUP_DIR/todo_db_$DATE.sql

# 保留最近30天的备份
find $BACKUP_DIR -name "*.sql" -mtime +30 -delete
```

### 2. 文件备份
```bash
#!/bin/bash
# /usr/local/bin/backup-files.sh

BACKUP_DIR="/var/backups/todo-app"
DATE=$(date +%Y%m%d_%H%M%S)

tar -czf $BACKUP_DIR/todo_app_files_$DATE.tar.gz /var/www/todo-app

# 保留最近7天的文件备份
find $BACKUP_DIR -name "*files*.tar.gz" -mtime +7 -delete
```

### 3. 自动备份
```bash
# 添加到crontab
sudo crontab -e

# 每天凌晨2点备份数据库
0 2 * * * /usr/local/bin/backup-db.sh

# 每周日凌晨3点备份文件
0 3 * * 0 /usr/local/bin/backup-files.sh
```

## 故障排除

### 常见问题

1. **API返回500错误**
   - 检查PHP错误日志: `tail -f /var/log/php8.1-fpm.log`
   - 检查应用日志: `tail -f /var/www/todo-app/backend/runtime/log/`

2. **数据库连接失败**
   - 检查数据库服务: `sudo systemctl status mysql`
   - 验证连接配置: `mysql -u todo_user -p -h localhost todo_db`

3. **前端页面空白**
   - 检查Nginx错误日志: `tail -f /var/log/nginx/error.log`
   - 验证构建文件: `ls -la /var/www/todo-app/frontend/dist/`

4. **权限问题**
   - 检查文件权限: `ls -la /var/www/todo-app/`
   - 重置权限: `sudo chown -R www-data:www-data /var/www/todo-app/backend/runtime`

## 安全建议

1. **定期更新系统和软件包**
2. **使用强密码和密钥认证**
3. **配置防火墙规则**
4. **定期备份数据**
5. **监控系统日志**
6. **使用HTTPS加密传输**
7. **限制数据库访问权限**

## 维护清单

### 日常维护
- [ ] 检查系统资源使用情况
- [ ] 查看应用错误日志
- [ ] 验证备份完整性
- [ ] 监控API响应时间

### 周期维护
- [ ] 更新系统安全补丁
- [ ] 清理旧日志文件
- [ ] 检查SSL证书有效期
- [ ] 优化数据库性能

### 应急响应
- [ ] 准备回滚方案
- [ ] 建立监控告警
- [ ] 制定故障处理流程
- [ ] 定期演练恢复程序
