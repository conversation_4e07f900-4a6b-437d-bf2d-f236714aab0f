# Todo List 项目

一个使用 ThinkPHP 8 + Vue 3 构建的现代化 Todo List 应用，涵盖了两个框架的核心知识点和最佳实践。

## 项目结构

```
todo-project/
├── backend/                    # ThinkPHP 8 后端 API
│   ├── app/
│   │   ├── controller/        # 控制器
│   │   ├── model/            # 数据模型
│   │   ├── middleware/       # 中间件
│   │   ├── validate/         # 验证器
│   │   └── common/           # 公共类
│   ├── config/               # 配置文件
│   ├── database/             # 数据库相关
│   ├── docs/                 # API文档
│   └── route/                # 路由配置
├── frontend/                  # Vue 3 前端应用
│   ├── src/
│   │   ├── api/              # API服务
│   │   ├── components/       # 组件
│   │   ├── stores/           # Pinia状态管理
│   │   ├── router/           # 路由配置
│   │   ├── types/            # TypeScript类型
│   │   ├── utils/            # 工具函数
│   │   └── views/            # 页面组件
│   └── public/               # 静态资源
└── README.md                 # 项目说明
```

## 技术栈

### 后端 (ThinkPHP 8)
- **框架**: ThinkPHP 8.0
- **数据库**: MySQL 8.0
- **认证**: JWT (JSON Web Token)
- **验证**: ThinkPHP 验证器
- **中间件**: 认证中间件、CORS中间件
- **ORM**: ThinkORM
- **架构模式**: MVC + 服务层

### 前端 (Vue 3)
- **框架**: Vue 3 + Composition API
- **语言**: TypeScript
- **构建工具**: Vite
- **路由**: Vue Router 4
- **状态管理**: Pinia
- **UI组件**: Element Plus
- **HTTP客户端**: Axios
- **样式**: CSS3 + 响应式设计

## 功能特性

### 用户管理
- ✅ 用户注册/登录
- ✅ JWT Token认证
- ✅ 用户信息管理
- ✅ 密码修改
- ✅ 路由守卫

### 待办事项管理
- ✅ 创建、编辑、删除待办事项
- ✅ 任务状态切换（待完成/已完成）
- ✅ 优先级设置（低/中/高）
- ✅ 截止时间设置
- ✅ 批量操作（批量完成、删除）
- ✅ 搜索和筛选
- ✅ 分页显示

### 分类管理
- ✅ 创建、编辑、删除分类
- ✅ 自定义分类颜色
- ✅ 分类排序
- ✅ 分类统计

### 数据统计
- ✅ 任务完成率统计
- ✅ 分类任务统计
- ✅ 优先级分布统计
- ✅ 可视化图表展示

### 用户体验
- ✅ 响应式设计，支持移动端
- ✅ 现代化UI界面
- ✅ 实时数据更新
- ✅ 友好的错误提示
- ✅ 加载状态提示

## 环境要求

### 后端环境
- PHP >= 8.0
- MySQL >= 5.7
- Composer
- Apache/Nginx (可选，开发环境可使用内置服务器)

### 前端环境
- Node.js >= 16.0
- npm >= 8.0

## 安装部署

### 1. 克隆项目
```bash
git clone <repository-url>
cd todo-project
```

### 2. 后端配置

#### 安装依赖
```bash
cd backend
composer install
```

#### 配置环境
```bash
# 复制环境配置文件
cp .env.example .env

# 编辑 .env 文件，配置数据库连接
vim .env
```

#### 数据库初始化
```bash
# 创建数据库
mysql -u root -p
CREATE DATABASE todo_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

# 导入数据表结构和示例数据
mysql -u root -p todo_db < database/init.sql
```

#### 启动后端服务
```bash
# 使用内置服务器（开发环境）
php think run

# 或使用启动脚本
chmod +x start.sh
./start.sh
```

### 3. 前端配置

#### 安装依赖
```bash
cd frontend
npm install
```

#### 配置环境
```bash
# 编辑 .env 文件，配置API地址
vim .env
```

#### 启动前端服务
```bash
# 启动开发服务器
npm run dev

# 或使用启动脚本
chmod +x start.sh
./start.sh
```

## 访问地址

- **前端应用**: http://localhost:3000
- **后端API**: http://localhost:8000/api
- **API文档**: 查看 `backend/docs/api.md`

## 默认账户

- **用户名**: admin
- **邮箱**: <EMAIL>
- **密码**: password

## 核心知识点

### ThinkPHP 8 核心特性
1. **MVC架构**: 控制器、模型、视图分离
2. **路由系统**: RESTful API路由设计
3. **中间件**: 认证、CORS处理
4. **验证器**: 数据验证和错误处理
5. **ORM操作**: 模型关联、查询构造器
6. **JWT认证**: 无状态认证机制
7. **异常处理**: 统一错误响应格式

### Vue 3 核心特性
1. **Composition API**: 逻辑复用和组织
2. **TypeScript**: 类型安全开发
3. **Pinia状态管理**: 现代化状态管理方案
4. **Vue Router**: 路由守卫和懒加载
5. **组件化开发**: 可复用组件设计
6. **响应式系统**: ref、reactive使用
7. **生命周期**: onMounted等钩子函数

## 项目亮点

1. **现代化技术栈**: 使用最新版本的框架和工具
2. **TypeScript支持**: 完整的类型定义和类型安全
3. **最佳实践**: 遵循框架官方推荐的最佳实践
4. **代码规范**: 统一的代码风格和注释规范
5. **错误处理**: 完善的错误处理和用户提示
6. **响应式设计**: 适配各种设备屏幕
7. **性能优化**: 懒加载、防抖等优化措施

## API 文档

详细的API文档请查看: `backend/docs/api.md`

## 开发指南

### 后端开发
- 控制器位于 `app/controller/`
- 模型位于 `app/model/`
- 中间件位于 `app/middleware/`
- 验证器位于 `app/validate/`

### 前端开发
- 页面组件位于 `src/views/`
- 公共组件位于 `src/components/`
- API服务位于 `src/api/`
- 状态管理位于 `src/stores/`

## 许可证

MIT License
