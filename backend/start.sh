#!/bin/bash

# Todo List 后端启动脚本

echo "=== Todo List 后端启动 ==="

# 检查PHP版本
echo "检查PHP版本..."
php -v

# 检查Composer
echo "检查Composer..."
composer --version

# 安装依赖（如果需要）
if [ ! -d "vendor" ]; then
    echo "安装Composer依赖..."
    composer install
fi

# 检查数据库配置
echo "请确保已配置数据库连接信息在 .env 文件中"
echo "数据库初始化脚本位于: database/init.sql"

# 启动开发服务器
echo "启动ThinkPHP开发服务器..."
echo "访问地址: http://localhost:8000"
echo "API文档: backend/docs/api.md"
echo ""

php think run
