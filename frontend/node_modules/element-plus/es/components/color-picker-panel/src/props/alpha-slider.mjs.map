{"version": 3, "file": "alpha-slider.mjs", "sources": ["../../../../../../../packages/components/color-picker-panel/src/props/alpha-slider.ts"], "sourcesContent": ["import { buildProps, definePropType } from '@element-plus/utils'\n\nimport type { ExtractPropTypes, __ExtractPublicPropTypes } from 'vue'\nimport type Color from '../utils/color'\n\nexport const alphaSliderProps = buildProps({\n  color: {\n    type: definePropType<Color>(Object),\n    required: true,\n  },\n  vertical: <PERSON><PERSON><PERSON>,\n  disabled: Boolean,\n} as const)\n\nexport type AlphaSliderProps = ExtractPropTypes<typeof alphaSliderProps>\nexport type AlphaSliderPropsPublic = __ExtractPublicPropTypes<\n  typeof alphaSliderProps\n>\n"], "names": [], "mappings": ";;AACY,MAAC,gBAAgB,GAAG,UAAU,CAAC;AAC3C,EAAE,KAAK,EAAE;AACT,IAAI,IAAI,EAAE,cAAc,CAAC,MAAM,CAAC;AAChC,IAAI,QAAQ,EAAE,IAAI;AAClB,GAAG;AACH,EAAE,QAAQ,EAAE,OAAO;AACnB,EAAE,QAAQ,EAAE,OAAO;AACnB,CAAC;;;;"}