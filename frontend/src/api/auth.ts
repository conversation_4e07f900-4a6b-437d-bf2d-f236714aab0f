import { request } from './http'
import type {
  LoginRequest,
  RegisterRequest,
  AuthResponse,
  User,
  ChangePasswordRequest,
  UpdateProfileRequest,
  ApiResponse
} from '@/types'

// 认证相关API
export const authApi = {
  // 用户登录
  login(data: LoginRequest): Promise<ApiResponse<AuthResponse>> {
    return request.post('/auth/login', data)
  },

  // 用户注册
  register(data: RegisterRequest): Promise<ApiResponse<AuthResponse>> {
    return request.post('/auth/register', data)
  },

  // 获取用户信息
  getProfile(): Promise<ApiResponse<User>> {
    return request.get('/auth/profile')
  },

  // 更新用户信息
  updateProfile(data: UpdateProfileRequest): Promise<ApiResponse<User>> {
    return request.put('/auth/profile', data)
  },

  // 修改密码
  changePassword(data: ChangePasswordRequest): Promise<ApiResponse<null>> {
    return request.post('/auth/change-password', data)
  }
}
