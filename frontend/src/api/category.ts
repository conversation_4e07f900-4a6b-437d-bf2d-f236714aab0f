import { request } from './http'
import type {
  Category,
  CategoryRequest,
  SortRequest,
  ApiResponse
} from '@/types'

// 分类相关API
export const categoryApi = {
  // 获取分类列表
  getList(): Promise<ApiResponse<Category[]>> {
    return request.get('/categories')
  },

  // 创建分类
  create(data: CategoryRequest): Promise<ApiResponse<Category>> {
    return request.post('/categories', data)
  },

  // 获取分类详情
  getDetail(id: number): Promise<ApiResponse<Category>> {
    return request.get(`/categories/${id}`)
  },

  // 更新分类
  update(id: number, data: CategoryRequest): Promise<ApiResponse<Category>> {
    return request.put(`/categories/${id}`, data)
  },

  // 删除分类
  delete(id: number): Promise<ApiResponse<null>> {
    return request.delete(`/categories/${id}`)
  },

  // 分类排序
  sort(data: SortRequest): Promise<ApiResponse<null>> {
    return request.post('/categories/sort', data)
  }
}
