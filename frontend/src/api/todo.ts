import { request } from './http'
import type {
  Todo,
  TodoRequest,
  TodoQuery,
  BatchRequest,
  Statistics,
  ApiResponse,
  PaginatedResponse
} from '@/types'

// 待办事项相关API
export const todoApi = {
  // 获取待办事项列表（分页）
  getList(params?: TodoQuery): Promise<PaginatedResponse<Todo>> {
    return request.get('/todos', { params })
  },

  // 创建待办事项
  create(data: TodoRequest): Promise<ApiResponse<Todo>> {
    return request.post('/todos', data)
  },

  // 获取待办事项详情
  getDetail(id: number): Promise<ApiResponse<Todo>> {
    return request.get(`/todos/${id}`)
  },

  // 更新待办事项
  update(id: number, data: TodoRequest): Promise<ApiResponse<Todo>> {
    return request.put(`/todos/${id}`, data)
  },

  // 删除待办事项
  delete(id: number): Promise<ApiResponse<null>> {
    return request.delete(`/todos/${id}`)
  },

  // 切换完成状态
  toggleStatus(id: number): Promise<ApiResponse<Todo>> {
    return request.patch(`/todos/${id}/toggle`)
  },

  // 批量操作
  batch(data: BatchRequest): Promise<ApiResponse<null>> {
    return request.post('/todos/batch', data)
  },

  // 获取统计信息
  getStatistics(): Promise<ApiResponse<Statistics>> {
    return request.get('/todos/statistics')
  }
}
