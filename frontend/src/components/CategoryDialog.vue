<template>
  <el-dialog
    v-model="visible"
    :title="isEdit ? '编辑分类' : '新建分类'"
    width="500px"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="80px"
    >
      <el-form-item label="名称" prop="name">
        <el-input
          v-model="form.name"
          placeholder="请输入分类名称"
          maxlength="50"
          show-word-limit
        />
      </el-form-item>
      
      <el-form-item label="颜色" prop="color">
        <div class="color-picker-wrapper">
          <el-color-picker
            v-model="form.color"
            :predefine="predefineColors"
            show-alpha
          />
          <el-input
            v-model="form.color"
            placeholder="#1890ff"
            style="margin-left: 12px; flex: 1"
          />
        </div>
      </el-form-item>
      
      <el-form-item label="排序" prop="sort">
        <el-input-number
          v-model="form.sort"
          :min="0"
          :max="9999"
          placeholder="排序值，数字越小越靠前"
          style="width: 100%"
        />
      </el-form-item>
    </el-form>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button
          type="primary"
          :loading="loading"
          @click="handleSubmit"
        >
          {{ isEdit ? '更新' : '创建' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import type { FormInstance, FormRules } from 'element-plus'
import { useCategoryStore } from '@/stores'
import { generateRandomColor } from '@/utils'
import type { Category, CategoryRequest } from '@/types'

interface Props {
  modelValue: boolean
  category?: Category | null
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'success'): void
}

const props = withDefaults(defineProps<Props>(), {
  category: null
})

const emit = defineEmits<Emits>()

const categoryStore = useCategoryStore()

const formRef = ref<FormInstance>()
const loading = ref(false)

// 预定义颜色
const predefineColors = [
  '#1890ff',
  '#52c41a',
  '#faad14',
  '#f5222d',
  '#722ed1',
  '#fa541c',
  '#13c2c2',
  '#eb2f96',
  '#a0d911',
  '#fa8c16'
]

// 对话框显示状态
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 是否为编辑模式
const isEdit = computed(() => !!props.category)

// 表单数据
const form = reactive<CategoryRequest>({
  name: '',
  color: '#1890ff',
  sort: 0
})

// 验证规则
const rules: FormRules = {
  name: [
    { required: true, message: '请输入分类名称', trigger: 'blur' },
    { max: 50, message: '分类名称长度不能超过50个字符', trigger: 'blur' }
  ],
  color: [
    { required: true, message: '请选择颜色', trigger: 'change' },
    { pattern: /^#[0-9a-fA-F]{6}$/, message: '请输入正确的颜色值', trigger: 'blur' }
  ],
  sort: [
    { required: true, message: '请输入排序值', trigger: 'blur' },
    { type: 'number', min: 0, max: 9999, message: '排序值必须在0-9999之间', trigger: 'blur' }
  ]
}

// 重置表单
const resetForm = () => {
  form.name = ''
  form.color = generateRandomColor()
  form.sort = categoryStore.categories.length
  
  if (formRef.value) {
    formRef.value.clearValidate()
  }
}

// 填充表单数据
const fillForm = (category: Category) => {
  form.name = category.name
  form.color = category.color
  form.sort = category.sort
}

// 处理提交
const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    loading.value = true
    
    if (isEdit.value && props.category) {
      await categoryStore.updateCategory(props.category.id, form)
    } else {
      await categoryStore.createCategory(form)
    }
    
    emit('success')
  } catch (error) {
    console.error('提交失败:', error)
  } finally {
    loading.value = false
  }
}

// 处理关闭
const handleClose = () => {
  visible.value = false
}

// 监听对话框打开
watch(visible, (val) => {
  if (val) {
    if (props.category) {
      fillForm(props.category)
    } else {
      resetForm()
    }
  }
})
</script>

<style scoped>
.color-picker-wrapper {
  display: flex;
  align-items: center;
  width: 100%;
}

.dialog-footer {
  text-align: right;
}
</style>
