<template>
  <el-dialog
    v-model="visible"
    title="任务详情"
    width="600px"
  >
    <div v-if="todo" class="todo-detail">
      <div class="detail-header">
        <h2>{{ todo.title }}</h2>
        <div class="status-badge">
          <el-tag
            :type="getStatusInfo(todo.status).type"
            size="large"
          >
            {{ getStatusInfo(todo.status).text }}
          </el-tag>
        </div>
      </div>
      
      <div class="detail-content">
        <div class="detail-item">
          <label>内容：</label>
          <div class="content">
            {{ todo.content || '无' }}
          </div>
        </div>
        
        <div class="detail-item">
          <label>分类：</label>
          <div class="content">
            <el-tag
              v-if="todo.category"
              :color="todo.category.color"
              effect="light"
            >
              {{ todo.category.name }}
            </el-tag>
            <span v-else class="text-muted">未分类</span>
          </div>
        </div>
        
        <div class="detail-item">
          <label>优先级：</label>
          <div class="content">
            <el-tag
              :type="getPriorityInfo(todo.priority).type"
            >
              {{ getPriorityInfo(todo.priority).text }}
            </el-tag>
          </div>
        </div>
        
        <div class="detail-item">
          <label>截止时间：</label>
          <div class="content">
            <span v-if="todo.due_date" class="due-date">
              <el-icon><Clock /></el-icon>
              {{ formatDate(todo.due_date) }}
            </span>
            <span v-else class="text-muted">无</span>
          </div>
        </div>
        
        <div class="detail-item">
          <label>创建时间：</label>
          <div class="content">
            {{ formatDate(todo.create_time) }}
          </div>
        </div>
        
        <div class="detail-item">
          <label>更新时间：</label>
          <div class="content">
            {{ formatDate(todo.update_time) }}
          </div>
        </div>
        
        <div v-if="todo.completed_at" class="detail-item">
          <label>完成时间：</label>
          <div class="content">
            {{ formatDate(todo.completed_at) }}
          </div>
        </div>
      </div>
    </div>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="visible = false">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { formatDate, getPriorityInfo, getStatusInfo } from '@/utils'
import type { Todo } from '@/types'
import { Clock } from '@element-plus/icons-vue'

interface Props {
  modelValue: boolean
  todo?: Todo | null
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
}

const props = withDefaults(defineProps<Props>(), {
  todo: null
})

const emit = defineEmits<Emits>()

// 对话框显示状态
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})
</script>

<style scoped>
.todo-detail {
  padding: 8px 0;
}

.detail-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e8e8e8;
}

.detail-header h2 {
  flex: 1;
  font-size: 20px;
  color: #333;
  margin: 0;
  line-height: 1.4;
}

.status-badge {
  margin-left: 16px;
}

.detail-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.detail-item {
  display: flex;
  align-items: flex-start;
}

.detail-item label {
  width: 80px;
  font-weight: 500;
  color: #666;
  flex-shrink: 0;
}

.detail-item .content {
  flex: 1;
  color: #333;
  line-height: 1.5;
}

.text-muted {
  color: #999;
}

.due-date {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #f56c6c;
}

.dialog-footer {
  text-align: right;
}
</style>
