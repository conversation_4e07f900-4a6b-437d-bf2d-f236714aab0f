<template>
  <el-dialog
    v-model="visible"
    :title="isEdit ? '编辑任务' : '新建任务'"
    width="600px"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="80px"
    >
      <el-form-item label="标题" prop="title">
        <el-input
          v-model="form.title"
          placeholder="请输入任务标题"
          maxlength="200"
          show-word-limit
        />
      </el-form-item>
      
      <el-form-item label="内容" prop="content">
        <el-input
          v-model="form.content"
          type="textarea"
          :rows="4"
          placeholder="请输入任务内容（可选）"
          maxlength="1000"
          show-word-limit
        />
      </el-form-item>
      
      <el-form-item label="分类" prop="category_id">
        <el-select
          v-model="form.category_id"
          placeholder="请选择分类"
          clearable
          style="width: 100%"
        >
          <el-option
            v-for="category in categoryStore.categories"
            :key="category.id"
            :label="category.name"
            :value="category.id"
          >
            <div class="category-option">
              <div
                class="category-color"
                :style="{ backgroundColor: category.color }"
              ></div>
              <span>{{ category.name }}</span>
            </div>
          </el-option>
        </el-select>
      </el-form-item>
      
      <el-form-item label="优先级" prop="priority">
        <el-radio-group v-model="form.priority">
          <el-radio :label="1">
            <el-tag type="info" size="small">低</el-tag>
          </el-radio>
          <el-radio :label="2">
            <el-tag type="warning" size="small">中</el-tag>
          </el-radio>
          <el-radio :label="3">
            <el-tag type="danger" size="small">高</el-tag>
          </el-radio>
        </el-radio-group>
      </el-form-item>
      
      <el-form-item label="截止时间" prop="due_date">
        <el-date-picker
          v-model="form.due_date"
          type="datetime"
          placeholder="请选择截止时间（可选）"
          format="YYYY-MM-DD HH:mm"
          value-format="YYYY-MM-DD HH:mm:ss"
          style="width: 100%"
        />
      </el-form-item>
    </el-form>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button
          type="primary"
          :loading="loading"
          @click="handleSubmit"
        >
          {{ isEdit ? '更新' : '创建' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import type { FormInstance, FormRules } from 'element-plus'
import { useTodoStore, useCategoryStore } from '@/stores'
import type { Todo, TodoRequest } from '@/types'

interface Props {
  modelValue: boolean
  todo?: Todo | null
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'success'): void
}

const props = withDefaults(defineProps<Props>(), {
  todo: null
})

const emit = defineEmits<Emits>()

const todoStore = useTodoStore()
const categoryStore = useCategoryStore()

const formRef = ref<FormInstance>()
const loading = ref(false)

// 对话框显示状态
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 是否为编辑模式
const isEdit = computed(() => !!props.todo)

// 表单数据
const form = reactive<TodoRequest>({
  title: '',
  content: '',
  category_id: undefined,
  priority: 1,
  due_date: undefined
})

// 验证规则
const rules: FormRules = {
  title: [
    { required: true, message: '请输入任务标题', trigger: 'blur' },
    { max: 200, message: '标题长度不能超过200个字符', trigger: 'blur' }
  ],
  content: [
    { max: 1000, message: '内容长度不能超过1000个字符', trigger: 'blur' }
  ],
  priority: [
    { required: true, message: '请选择优先级', trigger: 'change' }
  ]
}

// 重置表单
const resetForm = () => {
  form.title = ''
  form.content = ''
  form.category_id = undefined
  form.priority = 1
  form.due_date = undefined
  
  if (formRef.value) {
    formRef.value.clearValidate()
  }
}

// 填充表单数据
const fillForm = (todo: Todo) => {
  form.title = todo.title
  form.content = todo.content || ''
  form.category_id = todo.category_id || undefined
  form.priority = todo.priority
  form.due_date = todo.due_date || undefined
}

// 处理提交
const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    loading.value = true
    
    if (isEdit.value && props.todo) {
      await todoStore.updateTodo(props.todo.id, form)
    } else {
      await todoStore.createTodo(form)
    }
    
    emit('success')
  } catch (error) {
    console.error('提交失败:', error)
  } finally {
    loading.value = false
  }
}

// 处理关闭
const handleClose = () => {
  visible.value = false
}

// 监听对话框打开
watch(visible, (val) => {
  if (val) {
    if (props.todo) {
      fillForm(props.todo)
    } else {
      resetForm()
    }
  }
})
</script>

<style scoped>
.category-option {
  display: flex;
  align-items: center;
  gap: 8px;
}

.category-color {
  width: 12px;
  height: 12px;
  border-radius: 50%;
}

.dialog-footer {
  text-align: right;
}

:deep(.el-radio) {
  margin-right: 24px;
}
</style>
