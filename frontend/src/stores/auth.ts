import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { authApi } from '@/api'
import type { User, LoginRequest, RegisterRequest } from '@/types'

export const useAuthStore = defineStore('auth', () => {
  // 状态
  const token = ref<string>(localStorage.getItem('token') || '')
  const user = ref<User | null>(null)
  const loading = ref(false)

  // 计算属性
  const isAuthenticated = computed(() => !!token.value && !!user.value)

  // 设置token
  const setToken = (newToken: string) => {
    token.value = newToken
    localStorage.setItem('token', newToken)
  }

  // 设置用户信息
  const setUser = (newUser: User) => {
    user.value = newUser
  }

  // 清除认证信息
  const clearAuth = () => {
    token.value = ''
    user.value = null
    localStorage.removeItem('token')
  }

  // 登录
  const login = async (loginData: LoginRequest) => {
    try {
      loading.value = true
      const response = await authApi.login(loginData)
      
      setToken(response.data.token)
      setUser(response.data.user)
      
      ElMessage.success('登录成功')
      return response.data
    } catch (error) {
      throw error
    } finally {
      loading.value = false
    }
  }

  // 注册
  const register = async (registerData: RegisterRequest) => {
    try {
      loading.value = true
      const response = await authApi.register(registerData)
      
      setToken(response.data.token)
      setUser(response.data.user)
      
      ElMessage.success('注册成功')
      return response.data
    } catch (error) {
      throw error
    } finally {
      loading.value = false
    }
  }

  // 获取用户信息
  const fetchProfile = async () => {
    try {
      const response = await authApi.getProfile()
      setUser(response.data)
      return response.data
    } catch (error) {
      // 如果获取用户信息失败，清除认证信息
      clearAuth()
      throw error
    }
  }

  // 更新用户信息
  const updateProfile = async (data: { username?: string; email?: string }) => {
    try {
      loading.value = true
      const response = await authApi.updateProfile(data)
      setUser(response.data)
      ElMessage.success('更新成功')
      return response.data
    } catch (error) {
      throw error
    } finally {
      loading.value = false
    }
  }

  // 修改密码
  const changePassword = async (data: {
    old_password: string
    new_password: string
    confirm_password: string
  }) => {
    try {
      loading.value = true
      await authApi.changePassword(data)
      ElMessage.success('密码修改成功')
    } catch (error) {
      throw error
    } finally {
      loading.value = false
    }
  }

  // 登出
  const logout = () => {
    clearAuth()
    ElMessage.success('已退出登录')
  }

  // 初始化认证状态
  const initAuth = async () => {
    if (token.value && !user.value) {
      try {
        await fetchProfile()
      } catch (error) {
        // 如果token无效，清除认证信息
        clearAuth()
      }
    }
  }

  return {
    // 状态
    token,
    user,
    loading,
    
    // 计算属性
    isAuthenticated,
    
    // 方法
    login,
    register,
    fetchProfile,
    updateProfile,
    changePassword,
    logout,
    initAuth,
    setToken,
    setUser,
    clearAuth
  }
})
