import { defineStore } from 'pinia'
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import { categoryApi } from '@/api'
import type { Category, CategoryRequest } from '@/types'

export const useCategoryStore = defineStore('category', () => {
  // 状态
  const categories = ref<Category[]>([])
  const loading = ref(false)

  // 获取分类列表
  const fetchCategories = async () => {
    try {
      loading.value = true
      const response = await categoryApi.getList()
      categories.value = response.data
      return response.data
    } catch (error) {
      throw error
    } finally {
      loading.value = false
    }
  }

  // 创建分类
  const createCategory = async (data: CategoryRequest) => {
    try {
      loading.value = true
      const response = await categoryApi.create(data)
      categories.value.push(response.data)
      ElMessage.success('分类创建成功')
      return response.data
    } catch (error) {
      throw error
    } finally {
      loading.value = false
    }
  }

  // 更新分类
  const updateCategory = async (id: number, data: CategoryRequest) => {
    try {
      loading.value = true
      const response = await categoryApi.update(id, data)
      
      const index = categories.value.findIndex(item => item.id === id)
      if (index !== -1) {
        categories.value[index] = response.data
      }
      
      ElMessage.success('分类更新成功')
      return response.data
    } catch (error) {
      throw error
    } finally {
      loading.value = false
    }
  }

  // 删除分类
  const deleteCategory = async (id: number) => {
    try {
      loading.value = true
      await categoryApi.delete(id)
      
      const index = categories.value.findIndex(item => item.id === id)
      if (index !== -1) {
        categories.value.splice(index, 1)
      }
      
      ElMessage.success('分类删除成功')
    } catch (error) {
      throw error
    } finally {
      loading.value = false
    }
  }

  // 分类排序
  const sortCategories = async (sorts: Array<{ id: number; sort: number }>) => {
    try {
      loading.value = true
      await categoryApi.sort({ sorts })
      
      // 更新本地排序
      sorts.forEach(item => {
        const category = categories.value.find(cat => cat.id === item.id)
        if (category) {
          category.sort = item.sort
        }
      })
      
      // 重新排序
      categories.value.sort((a, b) => a.sort - b.sort)
      
      ElMessage.success('排序更新成功')
    } catch (error) {
      throw error
    } finally {
      loading.value = false
    }
  }

  // 根据ID获取分类
  const getCategoryById = (id: number) => {
    return categories.value.find(item => item.id === id)
  }

  // 重置状态
  const resetState = () => {
    categories.value = []
    loading.value = false
  }

  return {
    // 状态
    categories,
    loading,
    
    // 方法
    fetchCategories,
    createCategory,
    updateCategory,
    deleteCategory,
    sortCategories,
    getCategoryById,
    resetState
  }
})
