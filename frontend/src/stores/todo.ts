import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { todoApi } from '@/api'
import type { Todo, TodoRequest, TodoQuery, Statistics } from '@/types'

export const useTodoStore = defineStore('todo', () => {
  // 状态
  const todos = ref<Todo[]>([])
  const statistics = ref<Statistics | null>(null)
  const loading = ref(false)
  const pagination = ref({
    total: 0,
    current_page: 1,
    per_page: 15,
    last_page: 1
  })

  // 计算属性
  const pendingTodos = computed(() => todos.value.filter(todo => todo.status === 0))
  const completedTodos = computed(() => todos.value.filter(todo => todo.status === 1))

  // 获取待办事项列表
  const fetchTodos = async (params?: TodoQuery) => {
    try {
      loading.value = true
      const response = await todoApi.getList(params)
      
      todos.value = response.data.list
      pagination.value = {
        total: response.data.total,
        current_page: response.data.current_page,
        per_page: response.data.per_page,
        last_page: response.data.last_page
      }
      
      return response.data
    } catch (error) {
      throw error
    } finally {
      loading.value = false
    }
  }

  // 创建待办事项
  const createTodo = async (data: TodoRequest) => {
    try {
      loading.value = true
      const response = await todoApi.create(data)
      
      // 如果是第一页，添加到列表开头
      if (pagination.value.current_page === 1) {
        todos.value.unshift(response.data)
      }
      
      ElMessage.success('待办事项创建成功')
      return response.data
    } catch (error) {
      throw error
    } finally {
      loading.value = false
    }
  }

  // 更新待办事项
  const updateTodo = async (id: number, data: TodoRequest) => {
    try {
      loading.value = true
      const response = await todoApi.update(id, data)
      
      const index = todos.value.findIndex(item => item.id === id)
      if (index !== -1) {
        todos.value[index] = response.data
      }
      
      ElMessage.success('待办事项更新成功')
      return response.data
    } catch (error) {
      throw error
    } finally {
      loading.value = false
    }
  }

  // 删除待办事项
  const deleteTodo = async (id: number) => {
    try {
      loading.value = true
      await todoApi.delete(id)
      
      const index = todos.value.findIndex(item => item.id === id)
      if (index !== -1) {
        todos.value.splice(index, 1)
      }
      
      ElMessage.success('待办事项删除成功')
    } catch (error) {
      throw error
    } finally {
      loading.value = false
    }
  }

  // 切换完成状态
  const toggleTodoStatus = async (id: number) => {
    try {
      const response = await todoApi.toggleStatus(id)
      
      const index = todos.value.findIndex(item => item.id === id)
      if (index !== -1) {
        todos.value[index] = response.data
      }
      
      return response.data
    } catch (error) {
      throw error
    }
  }

  // 批量操作
  const batchOperation = async (action: 'delete' | 'complete' | 'uncomplete', ids: number[]) => {
    try {
      loading.value = true
      await todoApi.batch({ action, ids })
      
      if (action === 'delete') {
        todos.value = todos.value.filter(todo => !ids.includes(todo.id))
        ElMessage.success('批量删除成功')
      } else {
        const status = action === 'complete' ? 1 : 0
        todos.value.forEach(todo => {
          if (ids.includes(todo.id)) {
            todo.status = status
            if (status === 1) {
              todo.completed_at = new Date().toISOString()
            } else {
              todo.completed_at = undefined
            }
          }
        })
        ElMessage.success(`批量${action === 'complete' ? '完成' : '取消完成'}成功`)
      }
    } catch (error) {
      throw error
    } finally {
      loading.value = false
    }
  }

  // 获取统计信息
  const fetchStatistics = async () => {
    try {
      const response = await todoApi.getStatistics()
      statistics.value = response.data
      return response.data
    } catch (error) {
      throw error
    }
  }

  // 根据ID获取待办事项
  const getTodoById = (id: number) => {
    return todos.value.find(item => item.id === id)
  }

  // 重置状态
  const resetState = () => {
    todos.value = []
    statistics.value = null
    loading.value = false
    pagination.value = {
      total: 0,
      current_page: 1,
      per_page: 15,
      last_page: 1
    }
  }

  return {
    // 状态
    todos,
    statistics,
    loading,
    pagination,
    
    // 计算属性
    pendingTodos,
    completedTodos,
    
    // 方法
    fetchTodos,
    createTodo,
    updateTodo,
    deleteTodo,
    toggleTodoStatus,
    batchOperation,
    fetchStatistics,
    getTodoById,
    resetState
  }
})
