// API响应类型
export interface ApiResponse<T = any> {
  code: number
  message: string
  data: T
}

// 分页响应类型
export interface PaginatedResponse<T = any> {
  code: number
  message: string
  data: {
    list: T[]
    total: number
    per_page: number
    current_page: number
    last_page: number
  }
}

// 用户类型
export interface User {
  id: number
  username: string
  email: string
  avatar?: string
  status: boolean
  create_time: string
  update_time: string
}

// 登录请求类型
export interface LoginRequest {
  username: string
  password: string
}

// 注册请求类型
export interface RegisterRequest {
  username: string
  email: string
  password: string
  confirm_password: string
}

// 登录响应类型
export interface AuthResponse {
  user: User
  token: string
}

// 分类类型
export interface Category {
  id: number
  user_id: number
  name: string
  color: string
  sort: number
  create_time: string
  update_time: string
}

// 分类请求类型
export interface CategoryRequest {
  name: string
  color?: string
  sort?: number
}

// 待办事项类型
export interface Todo {
  id: number
  user_id: number
  category_id?: number
  title: string
  content?: string
  priority: 1 | 2 | 3 // 1:低, 2:中, 3:高
  status: 0 | 1 // 0:待完成, 1:已完成
  due_date?: string
  completed_at?: string
  sort: number
  create_time: string
  update_time: string
  category?: Category
  priority_text?: string
  status_text?: string
}

// 待办事项请求类型
export interface TodoRequest {
  title: string
  content?: string
  category_id?: number
  priority?: 1 | 2 | 3
  due_date?: string
  sort?: number
}

// 待办事项查询参数
export interface TodoQuery {
  page?: number
  limit?: number
  status?: 0 | 1 | ''
  category_id?: number
  priority?: 1 | 2 | 3
  keyword?: string
}

// 批量操作请求
export interface BatchRequest {
  action: 'delete' | 'complete' | 'uncomplete'
  ids: number[]
}

// 统计信息类型
export interface Statistics {
  total: number
  completed: number
  pending: number
  completion_rate: number
  priority_stats: Array<{
    priority: number
    count: number
  }>
  category_stats: Array<{
    category_id: number
    count: number
    category?: Category
  }>
}

// 修改密码请求
export interface ChangePasswordRequest {
  old_password: string
  new_password: string
  confirm_password: string
}

// 更新用户信息请求
export interface UpdateProfileRequest {
  username?: string
  email?: string
}

// 排序请求
export interface SortRequest {
  sorts: Array<{
    id: number
    sort: number
  }>
}
