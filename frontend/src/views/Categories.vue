<template>
  <div class="categories">
    <div class="categories-header">
      <h1>分类管理</h1>
      <el-button type="primary" @click="showCreateDialog = true">
        <el-icon><Plus /></el-icon>
        新建分类
      </el-button>
    </div>
    
    <el-card class="categories-list">
      <div v-if="categoryStore.loading" class="loading">
        <el-skeleton :rows="3" animated />
      </div>
      
      <div v-else-if="categoryStore.categories.length === 0" class="empty">
        <el-empty description="暂无分类">
          <el-button type="primary" @click="showCreateDialog = true">
            创建第一个分类
          </el-button>
        </el-empty>
      </div>
      
      <div v-else class="category-grid">
        <div
          v-for="category in categoryStore.categories"
          :key="category.id"
          class="category-card"
        >
          <div class="category-header">
            <div class="category-info">
              <div
                class="category-color"
                :style="{ backgroundColor: category.color }"
              ></div>
              <h3>{{ category.name }}</h3>
            </div>
            
            <el-dropdown @command="(command) => handleCommand(command, category)">
              <el-button text>
                <el-icon><MoreFilled /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="edit">
                    <el-icon><Edit /></el-icon>
                    编辑
                  </el-dropdown-item>
                  <el-dropdown-item command="delete" divided>
                    <el-icon><Delete /></el-icon>
                    删除
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
          
          <div class="category-stats">
            <div class="stat-item">
              <span class="stat-label">创建时间</span>
              <span class="stat-value">{{ formatDate(category.create_time, 'YYYY-MM-DD') }}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">排序</span>
              <span class="stat-value">{{ category.sort }}</span>
            </div>
          </div>
        </div>
      </div>
    </el-card>
    
    <!-- 创建/编辑对话框 -->
    <CategoryDialog
      v-model="showCreateDialog"
      :category="editingCategory"
      @success="handleDialogSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'
import { ElMessageBox } from 'element-plus'
import { useCategoryStore } from '@/stores'
import { formatDate } from '@/utils'
import type { Category } from '@/types'
import CategoryDialog from '@/components/CategoryDialog.vue'
import {
  Plus,
  MoreFilled,
  Edit,
  Delete
} from '@element-plus/icons-vue'

const categoryStore = useCategoryStore()

const showCreateDialog = ref(false)
const editingCategory = ref<Category | null>(null)

// 处理下拉菜单命令
const handleCommand = async (command: string, category: Category) => {
  switch (command) {
    case 'edit':
      editCategory(category)
      break
    case 'delete':
      await deleteCategory(category.id)
      break
  }
}

// 编辑分类
const editCategory = (category: Category) => {
  editingCategory.value = category
  showCreateDialog.value = true
}

// 删除分类
const deleteCategory = async (id: number) => {
  try {
    await ElMessageBox.confirm('确定要删除这个分类吗？删除后该分类下的任务将变为未分类。', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    await categoryStore.deleteCategory(id)
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除失败:', error)
    }
  }
}

// 对话框成功回调
const handleDialogSuccess = () => {
  showCreateDialog.value = false
  editingCategory.value = null
}

// 监听对话框关闭
watch(showCreateDialog, (val) => {
  if (!val) {
    editingCategory.value = null
  }
})

onMounted(() => {
  categoryStore.fetchCategories()
})
</script>

<style scoped>
.categories {
  max-width: 1200px;
  margin: 0 auto;
}

.categories-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.categories-header h1 {
  font-size: 24px;
  color: #333;
}

.loading,
.empty {
  padding: 40px 0;
  text-align: center;
}

.category-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 16px;
}

.category-card {
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  padding: 20px;
  background: #fff;
  transition: all 0.3s;
}

.category-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.category-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.category-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.category-color {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  flex-shrink: 0;
}

.category-info h3 {
  font-size: 16px;
  color: #333;
  margin: 0;
}

.category-stats {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.stat-label {
  font-size: 14px;
  color: #666;
}

.stat-value {
  font-size: 14px;
  color: #333;
  font-weight: 500;
}

@media (max-width: 768px) {
  .categories-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }
  
  .category-grid {
    grid-template-columns: 1fr;
  }
}
</style>
