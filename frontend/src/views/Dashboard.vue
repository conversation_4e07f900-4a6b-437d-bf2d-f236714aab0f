<template>
  <div class="dashboard">
    <div class="dashboard-header">
      <h1>仪表盘</h1>
      <p>欢迎回来，{{ authStore.user?.username }}！</p>
    </div>
    
    <!-- 统计卡片 -->
    <div class="stats-grid">
      <el-card class="stat-card">
        <div class="stat-content">
          <div class="stat-icon total">
            <el-icon><List /></el-icon>
          </div>
          <div class="stat-info">
            <h3>{{ statistics?.total || 0 }}</h3>
            <p>总任务数</p>
          </div>
        </div>
      </el-card>
      
      <el-card class="stat-card">
        <div class="stat-content">
          <div class="stat-icon pending">
            <el-icon><Clock /></el-icon>
          </div>
          <div class="stat-info">
            <h3>{{ statistics?.pending || 0 }}</h3>
            <p>待完成</p>
          </div>
        </div>
      </el-card>
      
      <el-card class="stat-card">
        <div class="stat-content">
          <div class="stat-icon completed">
            <el-icon><Check /></el-icon>
          </div>
          <div class="stat-info">
            <h3>{{ statistics?.completed || 0 }}</h3>
            <p>已完成</p>
          </div>
        </div>
      </el-card>
      
      <el-card class="stat-card">
        <div class="stat-content">
          <div class="stat-icon rate">
            <el-icon><TrendCharts /></el-icon>
          </div>
          <div class="stat-info">
            <h3>{{ statistics?.completion_rate || 0 }}%</h3>
            <p>完成率</p>
          </div>
        </div>
      </el-card>
    </div>
    
    <!-- 内容区域 -->
    <div class="dashboard-content">
      <!-- 最近任务 -->
      <el-card class="recent-todos">
        <template #header>
          <div class="card-header">
            <span>最近任务</span>
            <router-link to="/todos" class="more-link">查看全部</router-link>
          </div>
        </template>
        
        <div v-if="loading" class="loading">
          <el-skeleton :rows="3" animated />
        </div>
        
        <div v-else-if="recentTodos.length === 0" class="empty">
          <el-empty description="暂无任务" />
        </div>
        
        <div v-else class="todo-list">
          <div
            v-for="todo in recentTodos"
            :key="todo.id"
            class="todo-item"
            :class="{ completed: todo.status === 1 }"
          >
            <el-checkbox
              :model-value="todo.status === 1"
              @change="toggleTodoStatus(todo.id)"
            />
            <div class="todo-content">
              <h4>{{ todo.title }}</h4>
              <div class="todo-meta">
                <el-tag
                  v-if="todo.category"
                  :color="todo.category.color"
                  size="small"
                  effect="light"
                >
                  {{ todo.category.name }}
                </el-tag>
                <el-tag
                  :type="getPriorityInfo(todo.priority).type"
                  size="small"
                >
                  {{ getPriorityInfo(todo.priority).text }}
                </el-tag>
                <span class="create-time">
                  {{ formatRelativeTime(todo.create_time) }}
                </span>
              </div>
            </div>
          </div>
        </div>
      </el-card>
      
      <!-- 分类统计 -->
      <el-card class="category-stats">
        <template #header>
          <span>分类统计</span>
        </template>
        
        <div v-if="statistics?.category_stats?.length === 0" class="empty">
          <el-empty description="暂无数据" />
        </div>
        
        <div v-else class="category-list">
          <div
            v-for="item in statistics?.category_stats"
            :key="item.category_id"
            class="category-item"
          >
            <div class="category-info">
              <div
                class="category-color"
                :style="{ backgroundColor: item.category?.color || '#ccc' }"
              ></div>
              <span class="category-name">
                {{ item.category?.name || '未分类' }}
              </span>
            </div>
            <span class="category-count">{{ item.count }}</span>
          </div>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useAuthStore, useTodoStore } from '@/stores'
import { formatRelativeTime, getPriorityInfo } from '@/utils'
import type { Todo, Statistics } from '@/types'
import {
  List,
  Clock,
  Check,
  TrendCharts
} from '@element-plus/icons-vue'

const authStore = useAuthStore()
const todoStore = useTodoStore()

const loading = ref(false)
const recentTodos = ref<Todo[]>([])
const statistics = ref<Statistics | null>(null)

// 获取数据
const fetchData = async () => {
  try {
    loading.value = true
    
    // 获取最近的待办事项
    const todosResponse = await todoStore.fetchTodos({ limit: 5 })
    recentTodos.value = todosResponse.list
    
    // 获取统计信息
    statistics.value = await todoStore.fetchStatistics()
  } catch (error) {
    console.error('获取数据失败:', error)
  } finally {
    loading.value = false
  }
}

// 切换任务状态
const toggleTodoStatus = async (id: number) => {
  try {
    await todoStore.toggleTodoStatus(id)
    // 更新本地数据
    const todo = recentTodos.value.find(t => t.id === id)
    if (todo) {
      todo.status = todo.status === 1 ? 0 : 1
    }
    // 重新获取统计信息
    statistics.value = await todoStore.fetchStatistics()
  } catch (error) {
    console.error('切换状态失败:', error)
  }
}

onMounted(() => {
  fetchData()
})
</script>

<style scoped>
.dashboard {
  max-width: 1200px;
  margin: 0 auto;
}

.dashboard-header {
  margin-bottom: 24px;
}

.dashboard-header h1 {
  font-size: 24px;
  color: #333;
  margin-bottom: 8px;
}

.dashboard-header p {
  color: #666;
  font-size: 14px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
}

.stat-card {
  border-radius: 8px;
}

.stat-content {
  display: flex;
  align-items: center;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  font-size: 20px;
  color: #fff;
}

.stat-icon.total {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-icon.pending {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stat-icon.completed {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stat-icon.rate {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stat-info h3 {
  font-size: 24px;
  color: #333;
  margin-bottom: 4px;
}

.stat-info p {
  color: #666;
  font-size: 14px;
}

.dashboard-content {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 24px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.more-link {
  color: #1890ff;
  text-decoration: none;
  font-size: 14px;
}

.more-link:hover {
  text-decoration: underline;
}

.loading,
.empty {
  padding: 20px 0;
}

.todo-list {
  max-height: 400px;
  overflow-y: auto;
}

.todo-item {
  display: flex;
  align-items: flex-start;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.todo-item:last-child {
  border-bottom: none;
}

.todo-item.completed .todo-content h4 {
  text-decoration: line-through;
  color: #999;
}

.todo-content {
  flex: 1;
  margin-left: 12px;
}

.todo-content h4 {
  font-size: 14px;
  color: #333;
  margin-bottom: 8px;
}

.todo-meta {
  display: flex;
  align-items: center;
  gap: 8px;
}

.create-time {
  font-size: 12px;
  color: #999;
}

.category-list {
  max-height: 400px;
  overflow-y: auto;
}

.category-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.category-item:last-child {
  border-bottom: none;
}

.category-info {
  display: flex;
  align-items: center;
}

.category-color {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  margin-right: 8px;
}

.category-name {
  font-size: 14px;
  color: #333;
}

.category-count {
  font-size: 14px;
  color: #666;
  font-weight: 500;
}

@media (max-width: 768px) {
  .dashboard-content {
    grid-template-columns: 1fr;
  }
  
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}
</style>
