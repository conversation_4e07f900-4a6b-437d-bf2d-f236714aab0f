<template>
  <div class="profile">
    <div class="profile-header">
      <h1>个人中心</h1>
    </div>
    
    <div class="profile-content">
      <!-- 用户信息卡片 -->
      <el-card class="user-info-card">
        <template #header>
          <span>基本信息</span>
        </template>
        
        <div class="user-info">
          <div class="avatar-section">
            <el-avatar :size="80" :src="authStore.user?.avatar">
              {{ authStore.user?.username?.charAt(0).toUpperCase() }}
            </el-avatar>
            <el-button size="small" text>更换头像</el-button>
          </div>
          
          <div class="info-section">
            <div class="info-item">
              <label>用户名：</label>
              <span>{{ authStore.user?.username }}</span>
            </div>
            <div class="info-item">
              <label>邮箱：</label>
              <span>{{ authStore.user?.email }}</span>
            </div>
            <div class="info-item">
              <label>注册时间：</label>
              <span>{{ formatDate(authStore.user?.create_time || '') }}</span>
            </div>
            <div class="info-item">
              <label>状态：</label>
              <el-tag :type="authStore.user?.status ? 'success' : 'danger'">
                {{ authStore.user?.status ? '正常' : '禁用' }}
              </el-tag>
            </div>
          </div>
        </div>
        
        <div class="actions">
          <el-button type="primary" @click="showEditDialog = true">
            编辑信息
          </el-button>
          <el-button @click="showPasswordDialog = true">
            修改密码
          </el-button>
        </div>
      </el-card>
      
      <!-- 统计信息卡片 -->
      <el-card class="stats-card">
        <template #header>
          <span>使用统计</span>
        </template>
        
        <div v-if="statisticsLoading" class="loading">
          <el-skeleton :rows="3" animated />
        </div>
        
        <div v-else class="stats-grid">
          <div class="stat-item">
            <div class="stat-icon total">
              <el-icon><List /></el-icon>
            </div>
            <div class="stat-info">
              <h3>{{ statistics?.total || 0 }}</h3>
              <p>总任务数</p>
            </div>
          </div>
          
          <div class="stat-item">
            <div class="stat-icon completed">
              <el-icon><Check /></el-icon>
            </div>
            <div class="stat-info">
              <h3>{{ statistics?.completed || 0 }}</h3>
              <p>已完成</p>
            </div>
          </div>
          
          <div class="stat-item">
            <div class="stat-icon rate">
              <el-icon><TrendCharts /></el-icon>
            </div>
            <div class="stat-info">
              <h3>{{ statistics?.completion_rate || 0 }}%</h3>
              <p>完成率</p>
            </div>
          </div>
          
          <div class="stat-item">
            <div class="stat-icon categories">
              <el-icon><Collection /></el-icon>
            </div>
            <div class="stat-info">
              <h3>{{ categoryStore.categories.length }}</h3>
              <p>分类数</p>
            </div>
          </div>
        </div>
      </el-card>
    </div>
    
    <!-- 编辑信息对话框 -->
    <el-dialog
      v-model="showEditDialog"
      title="编辑信息"
      width="500px"
    >
      <el-form
        ref="editFormRef"
        :model="editForm"
        :rules="editRules"
        label-width="80px"
      >
        <el-form-item label="用户名" prop="username">
          <el-input
            v-model="editForm.username"
            placeholder="请输入用户名"
            maxlength="20"
          />
        </el-form-item>
        
        <el-form-item label="邮箱" prop="email">
          <el-input
            v-model="editForm.email"
            placeholder="请输入邮箱"
            maxlength="100"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showEditDialog = false">取消</el-button>
          <el-button
            type="primary"
            :loading="authStore.loading"
            @click="handleUpdateProfile"
          >
            更新
          </el-button>
        </div>
      </template>
    </el-dialog>
    
    <!-- 修改密码对话框 -->
    <el-dialog
      v-model="showPasswordDialog"
      title="修改密码"
      width="500px"
    >
      <el-form
        ref="passwordFormRef"
        :model="passwordForm"
        :rules="passwordRules"
        label-width="80px"
      >
        <el-form-item label="原密码" prop="old_password">
          <el-input
            v-model="passwordForm.old_password"
            type="password"
            placeholder="请输入原密码"
            show-password
          />
        </el-form-item>
        
        <el-form-item label="新密码" prop="new_password">
          <el-input
            v-model="passwordForm.new_password"
            type="password"
            placeholder="请输入新密码"
            show-password
          />
        </el-form-item>
        
        <el-form-item label="确认密码" prop="confirm_password">
          <el-input
            v-model="passwordForm.confirm_password"
            type="password"
            placeholder="请确认新密码"
            show-password
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showPasswordDialog = false">取消</el-button>
          <el-button
            type="primary"
            :loading="authStore.loading"
            @click="handleChangePassword"
          >
            修改
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import type { FormInstance, FormRules } from 'element-plus'
import { useAuthStore, useTodoStore, useCategoryStore } from '@/stores'
import { formatDate } from '@/utils'
import type { Statistics } from '@/types'
import {
  List,
  Check,
  TrendCharts,
  Collection
} from '@element-plus/icons-vue'

const authStore = useAuthStore()
const todoStore = useTodoStore()
const categoryStore = useCategoryStore()

const showEditDialog = ref(false)
const showPasswordDialog = ref(false)
const editFormRef = ref<FormInstance>()
const passwordFormRef = ref<FormInstance>()
const statisticsLoading = ref(false)
const statistics = ref<Statistics | null>(null)

// 编辑信息表单
const editForm = reactive({
  username: '',
  email: ''
})

// 修改密码表单
const passwordForm = reactive({
  old_password: '',
  new_password: '',
  confirm_password: ''
})

// 编辑信息验证规则
const editRules: FormRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '用户名长度在3-20个字符', trigger: 'blur' },
    { pattern: /^[a-zA-Z0-9_]+$/, message: '用户名只能包含字母、数字和下划线', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ]
}

// 修改密码验证规则
const validateConfirmPassword = (rule: any, value: string, callback: any) => {
  if (value !== passwordForm.new_password) {
    callback(new Error('两次密码输入不一致'))
  } else {
    callback()
  }
}

const passwordRules: FormRules = {
  old_password: [
    { required: true, message: '请输入原密码', trigger: 'blur' }
  ],
  new_password: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 6, max: 20, message: '密码长度在6-20个字符', trigger: 'blur' }
  ],
  confirm_password: [
    { required: true, message: '请确认密码', trigger: 'blur' },
    { validator: validateConfirmPassword, trigger: 'blur' }
  ]
}

// 获取统计信息
const fetchStatistics = async () => {
  try {
    statisticsLoading.value = true
    statistics.value = await todoStore.fetchStatistics()
  } catch (error) {
    console.error('获取统计信息失败:', error)
  } finally {
    statisticsLoading.value = false
  }
}

// 处理更新用户信息
const handleUpdateProfile = async () => {
  if (!editFormRef.value) return
  
  try {
    await editFormRef.value.validate()
    await authStore.updateProfile(editForm)
    showEditDialog.value = false
  } catch (error) {
    console.error('更新失败:', error)
  }
}

// 处理修改密码
const handleChangePassword = async () => {
  if (!passwordFormRef.value) return
  
  try {
    await passwordFormRef.value.validate()
    await authStore.changePassword(passwordForm)
    showPasswordDialog.value = false
    
    // 重置表单
    passwordForm.old_password = ''
    passwordForm.new_password = ''
    passwordForm.confirm_password = ''
  } catch (error) {
    console.error('修改密码失败:', error)
  }
}

// 初始化编辑表单
const initEditForm = () => {
  if (authStore.user) {
    editForm.username = authStore.user.username
    editForm.email = authStore.user.email
  }
}

onMounted(async () => {
  initEditForm()
  await Promise.all([
    fetchStatistics(),
    categoryStore.fetchCategories()
  ])
})
</script>

<style scoped>
.profile {
  max-width: 1000px;
  margin: 0 auto;
}

.profile-header {
  margin-bottom: 24px;
}

.profile-header h1 {
  font-size: 24px;
  color: #333;
}

.profile-content {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 24px;
}

.user-info {
  display: flex;
  gap: 24px;
  margin-bottom: 24px;
}

.avatar-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
}

.info-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.info-item {
  display: flex;
  align-items: center;
}

.info-item label {
  width: 80px;
  font-weight: 500;
  color: #666;
}

.info-item span {
  color: #333;
}

.actions {
  display: flex;
  gap: 12px;
}

.loading {
  padding: 20px 0;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.stat-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  color: #fff;
}

.stat-icon.total {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-icon.completed {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stat-icon.rate {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stat-icon.categories {
  background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
}

.stat-info h3 {
  font-size: 18px;
  color: #333;
  margin-bottom: 4px;
}

.stat-info p {
  color: #666;
  font-size: 12px;
}

.dialog-footer {
  text-align: right;
}

@media (max-width: 768px) {
  .profile-content {
    grid-template-columns: 1fr;
  }
  
  .user-info {
    flex-direction: column;
    align-items: center;
    text-align: center;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
  }
}
</style>
