<template>
  <div class="todos">
    <div class="todos-header">
      <h1>待办事项</h1>
      <el-button type="primary" @click="showCreateDialog = true">
        <el-icon><Plus /></el-icon>
        新建任务
      </el-button>
    </div>
    
    <!-- 筛选栏 -->
    <el-card class="filter-card">
      <el-form :model="filters" inline>
        <el-form-item label="状态">
          <el-select v-model="filters.status" placeholder="全部状态" clearable @change="handleFilter">
            <el-option label="待完成" :value="0" />
            <el-option label="已完成" :value="1" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="分类">
          <el-select v-model="filters.category_id" placeholder="全部分类" clearable @change="handleFilter">
            <el-option
              v-for="category in categoryStore.categories"
              :key="category.id"
              :label="category.name"
              :value="category.id"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="优先级">
          <el-select v-model="filters.priority" placeholder="全部优先级" clearable @change="handleFilter">
            <el-option label="低" :value="1" />
            <el-option label="中" :value="2" />
            <el-option label="高" :value="3" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="搜索">
          <el-input
            v-model="filters.keyword"
            placeholder="搜索标题或内容"
            clearable
            @input="handleSearch"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </el-form-item>
      </el-form>
    </el-card>
    
    <!-- 批量操作 -->
    <div v-if="selectedTodos.length > 0" class="batch-actions">
      <span>已选择 {{ selectedTodos.length }} 项</span>
      <el-button-group>
        <el-button size="small" @click="batchComplete">批量完成</el-button>
        <el-button size="small" @click="batchUncomplete">批量取消完成</el-button>
        <el-button size="small" type="danger" @click="batchDelete">批量删除</el-button>
      </el-button-group>
    </div>
    
    <!-- 任务列表 -->
    <el-card class="todos-list">
      <div v-if="todoStore.loading" class="loading">
        <el-skeleton :rows="5" animated />
      </div>
      
      <div v-else-if="todoStore.todos.length === 0" class="empty">
        <el-empty description="暂无任务">
          <el-button type="primary" @click="showCreateDialog = true">
            创建第一个任务
          </el-button>
        </el-empty>
      </div>
      
      <div v-else>
        <div
          v-for="todo in todoStore.todos"
          :key="todo.id"
          class="todo-item"
          :class="{ completed: todo.status === 1 }"
        >
          <el-checkbox
            :model-value="selectedTodos.includes(todo.id)"
            @change="toggleSelect(todo.id)"
          />
          
          <el-checkbox
            :model-value="todo.status === 1"
            @change="toggleTodoStatus(todo.id)"
            class="status-checkbox"
          />
          
          <div class="todo-content" @click="viewTodo(todo)">
            <h3>{{ todo.title }}</h3>
            <p v-if="todo.content" class="todo-description">{{ todo.content }}</p>
            
            <div class="todo-meta">
              <el-tag
                v-if="todo.category"
                :color="todo.category.color"
                size="small"
                effect="light"
              >
                {{ todo.category.name }}
              </el-tag>
              
              <el-tag
                :type="getPriorityInfo(todo.priority).type"
                size="small"
              >
                {{ getPriorityInfo(todo.priority).text }}
              </el-tag>
              
              <span v-if="todo.due_date" class="due-date">
                <el-icon><Clock /></el-icon>
                {{ formatDate(todo.due_date, 'MM-DD HH:mm') }}
              </span>
              
              <span class="create-time">
                {{ formatRelativeTime(todo.create_time) }}
              </span>
            </div>
          </div>
          
          <div class="todo-actions">
            <el-button size="small" text @click="editTodo(todo)">
              <el-icon><Edit /></el-icon>
            </el-button>
            <el-button size="small" text type="danger" @click="deleteTodo(todo.id)">
              <el-icon><Delete /></el-icon>
            </el-button>
          </div>
        </div>
        
        <!-- 分页 -->
        <div class="pagination">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :total="todoStore.pagination.total"
            :page-sizes="[10, 15, 20, 50]"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handlePageSizeChange"
            @current-change="handlePageChange"
          />
        </div>
      </div>
    </el-card>
    
    <!-- 创建/编辑对话框 -->
    <TodoDialog
      v-model="showCreateDialog"
      :todo="editingTodo"
      @success="handleDialogSuccess"
    />
    
    <!-- 详情对话框 -->
    <TodoDetailDialog
      v-model="showDetailDialog"
      :todo="viewingTodo"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, watch } from 'vue'
import { ElMessageBox } from 'element-plus'
import { useTodoStore, useCategoryStore } from '@/stores'
import { formatDate, formatRelativeTime, getPriorityInfo, debounce } from '@/utils'
import type { Todo, TodoQuery } from '@/types'
import TodoDialog from '@/components/TodoDialog.vue'
import TodoDetailDialog from '@/components/TodoDetailDialog.vue'
import {
  Plus,
  Search,
  Clock,
  Edit,
  Delete
} from '@element-plus/icons-vue'

const todoStore = useTodoStore()
const categoryStore = useCategoryStore()

const showCreateDialog = ref(false)
const showDetailDialog = ref(false)
const editingTodo = ref<Todo | null>(null)
const viewingTodo = ref<Todo | null>(null)
const selectedTodos = ref<number[]>([])

const currentPage = ref(1)
const pageSize = ref(15)

// 筛选条件
const filters = reactive<TodoQuery>({
  status: '',
  category_id: undefined,
  priority: undefined,
  keyword: ''
})

// 获取数据
const fetchData = async () => {
  await todoStore.fetchTodos({
    ...filters,
    page: currentPage.value,
    limit: pageSize.value
  })
}

// 处理筛选
const handleFilter = () => {
  currentPage.value = 1
  fetchData()
}

// 处理搜索（防抖）
const handleSearch = debounce(() => {
  handleFilter()
}, 500)

// 处理分页
const handlePageChange = (page: number) => {
  currentPage.value = page
  fetchData()
}

const handlePageSizeChange = (size: number) => {
  pageSize.value = size
  currentPage.value = 1
  fetchData()
}

// 切换选择
const toggleSelect = (id: number) => {
  const index = selectedTodos.value.indexOf(id)
  if (index > -1) {
    selectedTodos.value.splice(index, 1)
  } else {
    selectedTodos.value.push(id)
  }
}

// 切换任务状态
const toggleTodoStatus = async (id: number) => {
  try {
    await todoStore.toggleTodoStatus(id)
  } catch (error) {
    console.error('切换状态失败:', error)
  }
}

// 查看任务详情
const viewTodo = (todo: Todo) => {
  viewingTodo.value = todo
  showDetailDialog.value = true
}

// 编辑任务
const editTodo = (todo: Todo) => {
  editingTodo.value = todo
  showCreateDialog.value = true
}

// 删除任务
const deleteTodo = async (id: number) => {
  try {
    await ElMessageBox.confirm('确定要删除这个任务吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    await todoStore.deleteTodo(id)
    selectedTodos.value = selectedTodos.value.filter(todoId => todoId !== id)
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除失败:', error)
    }
  }
}

// 批量操作
const batchComplete = async () => {
  try {
    await todoStore.batchOperation('complete', selectedTodos.value)
    selectedTodos.value = []
  } catch (error) {
    console.error('批量完成失败:', error)
  }
}

const batchUncomplete = async () => {
  try {
    await todoStore.batchOperation('uncomplete', selectedTodos.value)
    selectedTodos.value = []
  } catch (error) {
    console.error('批量取消完成失败:', error)
  }
}

const batchDelete = async () => {
  try {
    await ElMessageBox.confirm(`确定要删除选中的 ${selectedTodos.value.length} 个任务吗？`, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    await todoStore.batchOperation('delete', selectedTodos.value)
    selectedTodos.value = []
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量删除失败:', error)
    }
  }
}

// 对话框成功回调
const handleDialogSuccess = () => {
  showCreateDialog.value = false
  editingTodo.value = null
  fetchData()
}

// 监听对话框关闭
watch(showCreateDialog, (val) => {
  if (!val) {
    editingTodo.value = null
  }
})

onMounted(async () => {
  await categoryStore.fetchCategories()
  await fetchData()
})
</script>

<style scoped>
.todos {
  max-width: 1200px;
  margin: 0 auto;
}

.todos-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.todos-header h1 {
  font-size: 24px;
  color: #333;
}

.filter-card {
  margin-bottom: 16px;
}

.batch-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background-color: #e6f7ff;
  border: 1px solid #91d5ff;
  border-radius: 6px;
  margin-bottom: 16px;
}

.loading,
.empty {
  padding: 40px 0;
  text-align: center;
}

.todo-item {
  display: flex;
  align-items: flex-start;
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
  transition: background-color 0.3s;
}

.todo-item:hover {
  background-color: #fafafa;
}

.todo-item:last-child {
  border-bottom: none;
}

.todo-item.completed .todo-content h3 {
  text-decoration: line-through;
  color: #999;
}

.status-checkbox {
  margin: 0 12px;
}

.todo-content {
  flex: 1;
  cursor: pointer;
}

.todo-content h3 {
  font-size: 16px;
  color: #333;
  margin-bottom: 8px;
}

.todo-description {
  color: #666;
  font-size: 14px;
  margin-bottom: 12px;
  line-height: 1.5;
}

.todo-meta {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.due-date {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #f56c6c;
}

.create-time {
  font-size: 12px;
  color: #999;
}

.todo-actions {
  display: flex;
  gap: 8px;
}

.pagination {
  display: flex;
  justify-content: center;
  margin-top: 24px;
}

@media (max-width: 768px) {
  .todos-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }
  
  .batch-actions {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
  
  .todo-item {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .todo-actions {
    margin-top: 12px;
    align-self: flex-end;
  }
}
</style>
