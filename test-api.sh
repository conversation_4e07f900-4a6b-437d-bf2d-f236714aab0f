#!/bin/bash

# Todo List API 测试脚本

echo "=== Todo List API 测试 ==="

API_BASE="http://localhost:8000/api"

echo "1. 测试API服务器连接..."
curl -s "$API_BASE/" | jq '.' || echo "API服务器未启动或JSON解析失败"

echo -e "\n2. 测试用户注册..."
REGISTER_RESPONSE=$(curl -s -X POST "$API_BASE/auth/register" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "testuser",
    "email": "<EMAIL>",
    "password": "123456",
    "confirm_password": "123456"
  }')

echo "$REGISTER_RESPONSE" | jq '.'

# 提取token
TOKEN=$(echo "$REGISTER_RESPONSE" | jq -r '.data.token // empty')

if [ -n "$TOKEN" ]; then
  echo -e "\n3. 测试获取用户信息..."
  curl -s -X GET "$API_BASE/auth/profile" \
    -H "Authorization: Bearer $TOKEN" | jq '.'

  echo -e "\n4. 测试创建分类..."
  curl -s -X POST "$API_BASE/categories" \
    -H "Authorization: Bearer $TOKEN" \
    -H "Content-Type: application/json" \
    -d '{
      "name": "测试分类",
      "color": "#1890ff",
      "sort": 1
    }' | jq '.'

  echo -e "\n5. 测试获取分类列表..."
  curl -s -X GET "$API_BASE/categories" \
    -H "Authorization: Bearer $TOKEN" | jq '.'

  echo -e "\n6. 测试创建待办事项..."
  curl -s -X POST "$API_BASE/todos" \
    -H "Authorization: Bearer $TOKEN" \
    -H "Content-Type: application/json" \
    -d '{
      "title": "测试任务",
      "content": "这是一个测试任务",
      "priority": 2
    }' | jq '.'

  echo -e "\n7. 测试获取待办事项列表..."
  curl -s -X GET "$API_BASE/todos" \
    -H "Authorization: Bearer $TOKEN" | jq '.'

  echo -e "\n8. 测试获取统计信息..."
  curl -s -X GET "$API_BASE/todos/statistics" \
    -H "Authorization: Bearer $TOKEN" | jq '.'

else
  echo "注册失败，无法继续测试"
fi

echo -e "\n=== 测试完成 ==="
